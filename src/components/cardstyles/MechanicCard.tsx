import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import HorizontalDivider from '../common/HorizontalDivider';
import { AppCommonIcons, DashboardIcons } from '../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';

interface MechanicCardProps {
  id: number;
  name: string;
  address: string;
  userRating: number;
  ratingOutOf: number;
  availability: string;
  isFavorite?: boolean;
  onFavoriteToggle?: (id: number) => void;
  onCardPress?: (id: number) => void;
  showFavoriteIcon?: boolean;
  availabilityColor?: string;
  cardStyle?: object;

}

const MechanicCard = ({
  id,
  name,
  address,
  userRating,
  ratingOutOf,
  availability,
  isFavorite = false,
  onFavoriteToggle,
  onCardPress,
  showFavoriteIcon = true,
  availabilityColor,
  cardStyle,
}: MechanicCardProps) => {

  const handleCardPress = () => {
    if (onCardPress) {
      onCardPress(id);
    }
  };

  const handleFavoritePress = () => {
    if (onFavoriteToggle) {
      onFavoriteToggle(id);
    }
  };

  const getAvailabilityColor = () => {
    if (availabilityColor) {
      return availabilityColor;
    }
    return availability === 'Open' ? Colors.PRIMARY : Colors.PRIMARY;
  };

  return (
    <TouchableOpacity
      style={[styles.mechanicCard, cardStyle]}
      onPress={handleCardPress}
      activeOpacity={onCardPress ? 0.7 : 1}
    >
      {/* Top Row: Avatar, Name, Address */}
      <View style={styles.mechanicTopRow}>
        <Image
          source={AppCommonIcons.MCX_USER_PROFILE_PIC}
          style={styles.mechanicAvatar}
        />
        <View style={styles.mechanicDetails}>
          <View style={styles.nameRow}>
            <Text style={styles.mechanicName}>{name}</Text>
            {showFavoriteIcon && (
              <TouchableOpacity
                style={styles.heartContainer}
                onPress={handleFavoritePress}
              >
                <Icon
                  name={isFavorite ? 'favorite' : 'favorite-border'}
                  size={20}
                  color={isFavorite ? Colors.PRIMARY : Colors.COMMON_GREY_SHADE_LIGHT}
                />
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.mechanicAddress}>{address}</Text>
        </View>
      </View>

      {/* Divider */}
      <HorizontalDivider />

      {/* Bottom Row: Stars and Availability */}
      <View style={styles.mechanicBottomRow}>
        <View style={styles.ratingRow}>
          {Array.from({ length: ratingOutOf }).map((_, i) => (
            <Image
              key={i}
              source={
                i < userRating
                  ? DashboardIcons.MCX_STAR_ICON
                  : DashboardIcons.MCX_LIGHT_STAR_ICON
              }
              style={styles.starIcon}
            />
          ))}
        </View>

        <View style={styles.availabilityContainer}>
          <Text style={styles.availabilityText}>
            <Text style={styles.availabilityLabel}>Availability </Text>
            <Text style={[
              styles.availabilityValue,
              { color: getAvailabilityColor() },
            ]}>
              {availability}
            </Text>
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  mechanicCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    position: 'relative',
  },
  mechanicTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  mechanicAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#eee',
    marginRight: 12,
  },
  mechanicDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mechanicName: {
    fontWeight: 'bold',
    fontSize: Sizes.LARGE,
    color: Colors.SECONDARY,
    fontFamily: Fonts.ROBO_REGULAR,
    flex: 1,
  },
  mechanicAddress: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_GREY_SHADE_DARK,
    marginTop: 2,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  heartContainer: {
    marginLeft: 8,
    padding: 4,
    alignSelf: 'flex-start',
    marginTop: -2,
  },
  mechanicBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starIcon: {
    width: 16,
    height: 16,
    marginRight: 2,
  },
  availabilityContainer: {
    alignItems: 'flex-end',
  },
  availabilityText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  availabilityLabel: {
    fontSize: Sizes.SMALL,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  availabilityValue: {
    fontSize: Sizes.MEDIUM,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_REGULAR,
  },

});

export default MechanicCard;
