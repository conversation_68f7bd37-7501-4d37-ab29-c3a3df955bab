import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import { AppStrings, ExceptionStrings } from '../../../../utils/constants/AppStrings';
import { Colors, CommonUIParams, Fonts, Sizes } from '../../../../utils/constants/Theme';

const LoadVinScreen = () => {
  const [vinNumber, setVinNumber] = useState('');

  const handleLoadVin = () => {
    if (!vinNumber.trim()) {
      Alert.alert(ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL, ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL);
      return;
    }
    // Handle VIN loading logic
    //console.log('Loading VIN:', vinNumber);
    Alert.alert('Success', 'VIN loaded successfully');
  };

  const handleContinue = () => {
    // Handle continue logic
    console.log('Continue pressed');
  };

  return (
    <View style={styles.container}>
      {/* Vehicle Section - Growing Container */}
      <View style={styles.vehicleSection}>
        <RegistrationTitleSection
          title={AppStrings.MCX_MY_VEHICLE_TITLE}
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />
        <View style={styles.inputMainContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_VIN_LABEL}</Text>
            <CommonTextInput
              value={vinNumber}
              onChangeText={setVinNumber}
              placeholder="Enter VIN number"
              style={styles.vinInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            />
          </View>
          <CustomButton
            text={AppStrings.MCX_LOAD_VIN_BUTTON}
            onPress={handleLoadVin}
            variant="primary"
            size="small"
            backgroundColor={Colors.PRIMARY}
            textColor="#fff"
            style={styles.loadVinButton}
            isBoldText={true}
          />
        </View>
      </View>
      {/* Continue Button - Fixed at Bottom of Screen */}
      <View style={styles.bottomContainer}>
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  vehicleSection: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  inputContainer: {
    marginBottom: 20,
    paddingTop: 26,
  },
  inputLabel: {
    fontSize: Sizes.SMALL,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  vinInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 2,
    marginLeft: 0,
    marginRight: 0,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  loadVinButton: {
    borderRadius: 2,
    paddingVertical: 10,
    minHeight: 'auto',
    marginTop: 14,
    marginBottom: 10,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  inputMainContainer: {
    paddingHorizontal: CommonUIParams.COMMON_CUSTOM_TAB_CONTENT_PADDING,
  }
});

export default LoadVinScreen;
