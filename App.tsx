/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 03/06/2025
 * Last Update: 03/06/2025
 *
 * @format
 */
import React, { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AppNavigator from './src/navigations/AppNavigator';
import { initializeFirebase } from './src/utils/configs/firebase';
import { AuthProvider } from './src/utils/configs/AuthContext';

function App(): React.ReactElement {
  useEffect(() => {
    initializeFirebase();
  }, []);

  return (
    <AuthProvider>
      <SafeAreaProvider>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <AppNavigator />
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </AuthProvider>
  );
}

export default App;
