import auth from '@react-native-firebase/auth';

export const initializeFirebase = async () => {
  try {
    // Firebase Auth initialization - no additional setup needed
    console.log('Firebase Auth initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Firebase:', error);
  }
};

export const signInWithEmailAndPassword = async (email: string, password: string) => {
  try {
    const userCredential = await auth().signInWithEmailAndPassword(email, password);
    return userCredential.user;
  } catch (error) {
    throw error;
  }
};

export const signUp = async (email: string, password: string) => {
  try {
    const userCredential = await auth().createUserWithEmailAndPassword(email, password);
    return userCredential.user;
  } catch (error) {
    throw error;
  }
};

export const signOut = async () => {
  try {
    await auth().signOut();
  } catch (error) {
    throw error;
  }
};

// Additional Firebase utilities can be added here if needed
