import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, StyleSheet, Alert } from 'react-native';
import { Colors, Fonts } from '../../utils/constants/Theme';
import { AppStrings, RouteNames } from '../../utils/constants/AppStrings';
import { wp } from '../../utils/ResponsiveParams';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../utils/configs/AuthContext';

const AppLoginPageScreen = () => {

  const navigation = useNavigation();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');

  const validateEmail = (value: string) => {
    // Simple email regex
    const re = /\S+@\S+\.\S+/;
    return re.test(value);
  };

  const handleEmailBlur = () => {
    if (email && !validateEmail(email)) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError('');
    }
  };
  const { signIn } = useAuth();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }
    try {
      await signIn(email, password);
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };
  return (
    <View style={styles.container}>
      <Image
        source={require('../../assets/mycanx_logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      {emailError ? (
        <Text style={{ color: 'red', alignSelf: 'flex-start', marginBottom: 4 }}>{emailError}</Text>
      ) : null}
      <TextInput
        style={[
          styles.input,
          emailError && { marginBottom: 14 }
        ]}
        placeholder={AppStrings.MCX_ENTER_EMAIL}
        placeholderTextColor="#fff"
        value={email}
        onChangeText={setEmail}
        onBlur={handleEmailBlur}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        style={styles.input}
        placeholder={AppStrings.MCX_ENTER_PASSWORD}
        placeholderTextColor="#fff"
        secureTextEntry
        value={password}
        onChangeText={setPassword}
      />

      <TouchableOpacity style={styles.loginBtn} onPress={handleLogin}>
        <Text style={styles.loginText}>{AppStrings.MCX_LOGIN.toUpperCase()}</Text>
      </TouchableOpacity>

      <View style={styles.bottomRow}>
        <TouchableOpacity>
          <Text style={styles.forgotText}>{AppStrings.MCX_FORGOT_PASSWORD}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.signUpBtn} onPress={() => navigation.navigate(RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never)}>
          <Text style={styles.signUpText}>{AppStrings.MCX_SIGN_UP.toUpperCase()}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AppLoginPageScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.BACKGROUND,
    padding: 14,
  },
  logo: {
    height: 180,
    width: 180,
    marginBottom: 40,
  },
  input: {
    width: '100%',
    backgroundColor: Colors.LOGIN_INPUT_TEXT_BG_COLOR,
    borderRadius: 2,
    paddingVertical: 10,
    paddingHorizontal: 16,
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 14,
    fontSize: 14,
    alignItems: 'center',
  },
  loginBtn: {
    width: '100%',
    backgroundColor: Colors.PRIMARY,
    borderRadius: 2,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 18,
  },
  loginText: {
    color: '#fff',
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 16,
  },
  bottomRow: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 8,
  },
  forgotText: {
    color: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    fontSize: 13,
  },
  signUpBtn: {
    borderColor: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    borderWidth: 1,
    borderRadius: 2,
    paddingVertical: 8,
    paddingHorizontal: wp(15),
    marginLeft: wp(20),
  },
  signUpText: {
    color: Colors.BOTTOM_SIGNUP_TEXT_COLOR,
    fontFamily: Fonts.ROBO_BOLD,
    fontSize: 14,
    fontWeight: 'bold',
  },
});
