import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomTab from '../../../components/common/CustomTabs';
import AppBar from '../../../components/common/AppBar';
import { registrationTabData } from '../../../utils/templates/TemplateConfig';
import { AppStrings } from '../../../utils/constants/AppStrings';
import { Colors } from '../../../utils/constants/Theme';
import RegistrationPersonalDetails from './components/RegistrationPersonalDetails';
import RegistrationAddressDetails from './components/RegistrationAddressDetails';
import LoadVinScreen from './components/LoadVinScreen';
import RegistrationPasswordScreen from './components/RegistrationPasswordScreen';
// Import tab components.

type TabType = keyof typeof registrationTabData;

const AccountRegistration = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<TabType>('1');

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  const handleBackPress = () => {
    navigation.navigate('LoginMainScreen' as never);
  };

  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        {Object.entries(registrationTabData).map(([key, label]) => (
          <CustomTab
            key={key}
            label={label}
            active={activeTab === key}
            onPress={() => handleTabPress(key as TabType)}
          />
        ))}
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case '1':
        return <RegistrationPersonalDetails />;
      case '2':
        return <RegistrationAddressDetails />;
      case '3':
        return <LoadVinScreen />;
      case '4':
        return <RegistrationPasswordScreen />;
      default:
        return <LoadVinScreen />;
    }
  };

  return (
    <View style={styles.mainContainer}>
      {/* Custom AppBar */}
      <AppBar
        showBackButton={true}
        showMailIcon={false}
        showChatIcon={false}
        showMenuIcon={false}
        showLogo={true}
        onBackPress={handleBackPress}
      />

      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}
      >
        {/* Title Section */}
        <TitleSection
          title={AppStrings.MCX_ACCOUNT_REGISTRATION_TITLE}
          bgColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.titleSection}
        />
        <View style={styles.container}>
          {renderTabs()}
          <View style={styles.contentContainer}>
            {renderTabContent()}
          </View>
        </View>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 20,
  },
  contentContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});

export default AccountRegistration;
