import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import BottomTabs from '../../utils/constants/BottomTabs';
import { Colors } from '../../utils/constants/Theme';

const BottomBar: React.FC<BottomTabBarProps> = ({
    state,
    navigation,
}) => (
    <View style={styles.container}>
        {state.routes.map((route, index) => {
            const isFocused = state.index === index;
            const tab = BottomTabs.find(t => t.key === route.name);

            if (!tab) return null;

            const onPress = () => {
                const event = navigation.emit({
                    type: 'tabPress',
                    target: route.key,
                    canPreventDefault: true,
                });

                if (!isFocused && !event.defaultPrevented) {
                    navigation.navigate(route.name);
                }
            };

            return (
                <TouchableOpacity
                    key={route.key}
                    accessibilityRole="button"
                    accessibilityState={isFocused ? { selected: true } : {}}
                    onPress={onPress}
                    style={[
                        styles.tab,
                        isFocused && { backgroundColor: Colors.TAB_BAR_BG_INACTIVE_COLOR }, // highlight active tab
                    ]}
                    activeOpacity={0.5}>
                    <Image
                        source={tab.icon}
                        style={[
                            styles.icon,
                            { tintColor: isFocused ? Colors.PRIMARY : Colors.TAB_IN_ACTIVE_COLOR },
                        ]}
                        resizeMode="contain"
                    />
                    <Text
                        style={[
                            styles.label,
                            { color: isFocused ? Colors.PRIMARY : Colors.TAB_IN_ACTIVE_COLOR },
                        ]}>
                        {tab.label}
                    </Text>
                </TouchableOpacity>
            );
        })}
    </View>
);

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        backgroundColor: Colors.TAB_BAR_BG_COLOR,
        alignItems: 'center',
        height: 60,
    },
    tab: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 6,
        borderWidth: 1,
        alignSelf: 'stretch',
        borderColor: Colors.TAB_BORDER_COLOR,
        height: '100%',
    },
    icon: {
        width: 28,
        height: 28,
        marginBottom: 2,
    },
    label: {
        fontSize: 8,
        fontWeight: 'bold',
        letterSpacing: 0.5,
    },
});

export default BottomBar;
