import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Colors } from '../utils/constants/Theme';
import { RouteNames } from '../utils/constants/AppStrings';
import DashBoard from '../screens/home/<USER>';
import Schedule from '../screens/features/schedule/Schedule';
import FindMechanics from '../screens/features/find_mechanics/FindMechanics';
import Appointments from '../screens/features/appointments/Appointments';
import BottomBar from '../components/common/BottomBar';

const Tab = createBottomTabNavigator();

const BottomTabNavigator = () => {
  return (
    <Tab.Navigator
      tabBar={props => <BottomBar {...props} />}
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: Colors.TAB_BORDER_COLOR,
        tabBarInactiveTintColor: Colors.TAB_BAR_BG_COLOR,
      }}>
      <Tab.Screen name={RouteNames.MCX_DASHBOARD} component={DashBoard} />
      <Tab.Screen name={RouteNames.MCX_SCHEDULE} component={Schedule} />
      <Tab.Screen name={RouteNames.MCX_FIND_MECHANIC} component={FindMechanics} />
      <Tab.Screen name={RouteNames.MCX_APPOINTMENTS} component={Appointments} />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
