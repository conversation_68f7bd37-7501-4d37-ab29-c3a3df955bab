import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Alert, ImageStyle, ViewStyle, TextStyle } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { AppStrings, RouteNames } from '../../utils/constants/AppStrings';
import CommonLink from '../../components/common/CommonLinkComponent';
import { useNavigation } from '@react-navigation/native';
import { Colors, Fonts } from '../../utils/constants/Theme';
import HorizontalDivider from '../../components/common/HorizontalDivider';
import auth from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

// Initialize Google Sign-In
GoogleSignin.configure({
  webClientId: '', // Get this from your Firebase console
  iosClientId: '', // Get this from your Firebase console
});

const LoginMainScreen = () => {
  const navigation = useNavigation();

  const onGoogleButtonPress = async () => {
    try {
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      const userInfo = await GoogleSignin.signIn();
      if (userInfo.idToken) {
        const googleCredential = auth.GoogleAuthProvider.credential(userInfo.idToken);
        await auth().signInWithCredential(googleCredential);
      } else {
        Alert.alert('Error', 'No ID token present in Google Sign In response');
      }
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  const onFacebookButtonPress = async () => {
    try {
      Alert.alert('Info', 'Facebook login will be implemented');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Image
        source={require('../../assets/mycanx_logo.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      <TouchableOpacity style={styles.signUpBtn} onPress={() => navigation.navigate(RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never)}>
        <Text style={styles.signUpText}>{AppStrings.MCX_SIGN_UP}</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.loginBtn} onPress={() => navigation.navigate(RouteNames.MCX_NAV_AppLoginPageScreen as never)}>
        <Text style={styles.loginText}>{AppStrings.MCX_LOGIN}</Text>
      </TouchableOpacity>

      <Text style={styles.orText}>{AppStrings.MCX_OR_WITH}</Text>

      <View style={styles.socialRow}>
        <TouchableOpacity
          style={[styles.socialButton, styles.facebookButton]}
          onPress={onFacebookButtonPress}
        >
          <Icon name="facebook" size={24} color={Colors.BUTTON_TEXT_COLOR} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.socialButton, styles.googleButton]}
          onPress={onGoogleButtonPress}
        >
          <Image
            source={require('../../assets/social-network-icons/google-icon.png')}
            style={styles.socialIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
      {/* Divider line */}
      <HorizontalDivider isFullWidth={true} />
      <Text style={styles.termsText}>
        {AppStrings.MCX_TERMS_PREFIX}{'\n'}
        <CommonLink url="https://your-privacy-policy-url.com" style={styles.linkText}>
          {AppStrings.MCX_PRIVACY_POLICY}
        </CommonLink> {AppStrings.MCX_AND_KEYWORD} <CommonLink url="https://your-terms-of-service-url.com" style={styles.linkText}>
          {AppStrings.MCX_TERMS_OF_SERVICE}
        </CommonLink>
      </Text>
    </View>
  );
};

export default LoginMainScreen;

type Styles = {
  container: ViewStyle;
  logo: ImageStyle;
  signUpBtn: ViewStyle;
  loginBtn: ViewStyle;
  socialRow: ViewStyle;
  socialButton: ViewStyle;
  facebookButton: ViewStyle;
  googleButton: ViewStyle;
  socialIcon: ImageStyle;
  signUpText: TextStyle;
  loginText: TextStyle;
  orText: TextStyle;
  termsText: TextStyle;
  linkText: TextStyle;
};

const styles = StyleSheet.create<Styles>({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.BACKGROUND,
  },
  logo: {
    height: 180,
    width: 180,
    marginBottom: 40,
  },
  signUpBtn: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 8,
    paddingHorizontal: 80,
    borderRadius: 2,
    marginBottom: 10,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 14,
  },
  loginBtn: {
    backgroundColor: 'transparent',
    borderColor: Colors.BORDER_COLOR,
    borderWidth: 1.2,
    paddingVertical: 8,
    paddingHorizontal: 80,
    borderRadius: 2,
    marginBottom: 30,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 14,
  },
  socialRow: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  socialButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.BORDER_COLOR,
    marginHorizontal: 12,
    padding: 10,
  },
  facebookButton: {
    backgroundColor: '#3b5998',
    borderColor: '#3b5998',
  },
  googleButton: {
    backgroundColor: Colors.BACKGROUND,
  },
  socialIcon: {
    width: 24,
    height: 24,
  },
  signUpText: {
    color: Colors.BUTTON_TEXT_COLOR,
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  loginText: {
    color: '#a10000',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: Fonts.ROBO_BOLD,
  },
  orText: {
    color: '#999',
    fontSize: 12,
    marginBottom: 10,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  termsText: {
    fontSize: 12,
    color: Colors.TERMS_TEXT_COLOR,
    textAlign: 'center',
    paddingHorizontal: 30,
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 25,
  },
  linkText: {
    color: Colors.LINK_TEXT_COLOR,
    fontWeight: 'bold',
    fontFamily: Fonts.ROBO_BOLD,
  },
});
