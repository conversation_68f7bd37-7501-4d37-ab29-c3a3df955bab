import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import SplashScreen from '../screens/splash/SplashScreen';
import LoginMainScreen from '../screens/login/LoginMainScreen';
import AppLoginPageScreen from '../screens/login/AppLoginPageScreen';
import DrawerNavigator from './DrawerNavigator';

import Schedule from '../screens/features/schedule/Schedule';
import SettingsScreen from '../screens/features/settings/SettingsScreen';
import MessagesScreen from '../screens/features/notifications/messages/MessagesScreen';
import PendingScreen from '../screens/features/notifications/messages/PendingScreen';
import FindMechanics from '../screens/features/find_mechanics/FindMechanics';
import Help from '../screens/features/legal/Help';
import ReferFriend from '../screens/features/referral/ReferFriend';
import AccountRegistration from '../screens/features/registration/AccountRegistration';
import { RouteNames } from '../utils/constants/AppStrings';
import EditProfile from '../screens/features/users/EditProfile';
import { useAuth } from '../utils/configs/AuthContext';

const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  const { user } = useAuth();

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false, animation: 'slide_from_right' }}>
        {!user ? (
          <>
            <Stack.Screen name={RouteNames.MCX_NAV_SPLASH} component={SplashScreen} />
            <Stack.Screen name={RouteNames.MCX_NAV_LoginMainScreen} component={LoginMainScreen} />
            <Stack.Screen name={RouteNames.MCX_NAV_ACCOUNT_REGISTRATION} component={AccountRegistration} />
            <Stack.Screen name={RouteNames.MCX_NAV_AppLoginPageScreen} component={AppLoginPageScreen} />
          </>
        ) : (
          <>
            <Stack.Screen name={RouteNames.MCX_NAV_DashBoard} component={DrawerNavigator} />
            <Stack.Screen name={RouteNames.MCX_NAV_MESSAGES} component={MessagesScreen} />
            <Stack.Screen name={RouteNames.MCX_NAV_PENDING} component={PendingScreen} />
            <Stack.Screen name={RouteNames.MCX_FIND_MECHANIC} component={FindMechanics} />
            <Stack.Screen name={RouteNames.MCX_SCHEDULE} component={Schedule} />
            <Stack.Screen name={RouteNames.MCX_NAV_SETTINGS} component={SettingsScreen} />
            <Stack.Screen name={RouteNames.MCX_NAV_HELP} component={Help} />
            <Stack.Screen name={RouteNames.MCX_NAV_REFER_FRIEND} component={ReferFriend} />
            <Stack.Screen name={RouteNames.MCX_NAV_EDIT_PROFILE} component={EditProfile} />
          </>
       )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
