import React from 'react';
import { View, ScrollView, StyleSheet, ImageBackground, ViewStyle } from 'react-native';
import { Colors, LayoutConstants } from '../../utils/constants/Theme';
import { AppCommonIcons } from '../../utils/constants/AppStrings';

interface ScreenLayoutProps {
    children: React.ReactNode;
    // Layout options
    useScrollView?: boolean;
    useImageBackground?: boolean;
    centerContent?: boolean;
    // Custom styling
    backgroundColor?: string;
    contentContainerStyle?: ViewStyle;
    scrollContainerStyle?: ViewStyle;
    // Padding options
    topPadding?: number;
    useHorizontalPadding?: boolean; // When true, applies 12px left and right padding
    // Fixed bottom section
    fixedBottomContent?: React.ReactNode;
    fixedBottomStyle?: ViewStyle;
}

const ScreenLayout = ({
    children,
    useScrollView = true,
    useImageBackground = true,
    centerContent = true,
    backgroundColor = 'transparent',
    contentContainerStyle,
    scrollContainerStyle,
    topPadding,
    useHorizontalPadding = false,
    fixedBottomContent,
    fixedBottomStyle,
}: ScreenLayoutProps) => {
    const renderContent = () => {
        if (useScrollView) {
            return (
                <ScrollView
                contentContainerStyle={[
                    styles.scrollContainer,
                    centerContent && styles.centeredContent,
                    topPadding !== undefined && { paddingTop: topPadding },
                    useHorizontalPadding && { paddingHorizontal: LayoutConstants.SCREEN_HORIZONTAL_PADDING },
                    scrollContainerStyle,
                ]}
                showsVerticalScrollIndicator={false}
            >
                {children}
            </ScrollView>
        );
    }

    return (
        <View style={[
            useHorizontalPadding ? styles.contentContainerWithPadding : styles.contentContainer,
            topPadding !== undefined && { paddingTop: topPadding },
            contentContainerStyle,
        ]}>
            {children}
        </View>
    );
};
    const containerContent = (
        <View style={[styles.container, { backgroundColor }]}>
            {renderContent()}
            {fixedBottomContent && (
                <View style={[styles.fixedBottomSection, fixedBottomStyle]}>
                    {fixedBottomContent}
                </View>
            )}
        </View>
    );

    if (useImageBackground) {
        return (
            <ImageBackground
                source={AppCommonIcons.MCX_BACKGROUND_IMAGE}
                style={styles.backgroundImage}
                resizeMode="cover"
            >
                {containerContent}
            </ImageBackground>
        );
    }

    return containerContent;
};

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    container: {
        flex: 1,
    },
    contentContainer: {
        flex: 1,
    },
    contentContainerWithPadding: {
        flex: 1,
        paddingHorizontal: LayoutConstants.SCREEN_HORIZONTAL_PADDING,
    },
    scrollContainer: {
        flexGrow: 1,
        paddingBottom: LayoutConstants.SCROLL_BOTTOM_PADDING,
    },
    centeredContent: {
        alignItems: 'center',
    },
    fixedBottomSection: {
        position: 'absolute',
        bottom: 0,
        left: LayoutConstants.HORIZONTAL_GAP_PERCENTAGE,
        right: LayoutConstants.HORIZONTAL_GAP_PERCENTAGE,
        width: LayoutConstants.CONTENT_WIDTH,
        backgroundColor: Colors.COMMON_WHITE_SHADE,
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
});

export default ScreenLayout;
