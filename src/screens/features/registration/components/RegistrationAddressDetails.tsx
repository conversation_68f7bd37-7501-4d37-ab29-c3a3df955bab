import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { AppStrings } from '../../../../utils/constants/AppStrings';

const RegistrationAddressDetails = () => {
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');

  const handleContinue = () => {
    console.log('Step 2 - Continue pressed');
  };

  return (
    <View style={styles.container}>
      <View style={styles.formSection}>
        <RegistrationTitleSection
          title={AppStrings.MCX_ADDRESS_INFO_TEXT}
          backgroundColor={Colors.COMMON_WHITE_SHADE}
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>{AppStrings.MCX_STREET_ADDRESS_LABEL}</Text>
          <CommonTextInput
            value={address}
            onChangeText={setAddress}
            placeholder={AppStrings.MCX_STREET_ADDRESS_TEXT}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>{AppStrings.MCX_ENTER_CITY_LABEL}</Text>
          <CommonTextInput
            value={city}
            onChangeText={setCity}
            placeholder={AppStrings.MCX_ENTER_CITY_TEXT}
            style={styles.textInput}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
          />
        </View>

        <View style={styles.rowContainer}>
          <View style={styles.halfInputContainer}>
            <Text style={styles.inputLabel}>State</Text>
            <CommonTextInput
              value={state}
              onChangeText={setState}
              placeholder={AppStrings.MCX_STATE_LABEL}
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            />
          </View>

          <View style={styles.halfInputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_ZIP_CODE_LABEL}</Text>
            <CommonTextInput
              value={zipCode}
              onChangeText={setZipCode}
              placeholder={AppStrings.MCX_ZIP_CODE_LABEL}
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              keyboardType="numeric"
            />
          </View>
        </View>
      </View>

      <View style={styles.bottomContainer}>
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    paddingBottom: 20,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  inputContainer: {
    marginBottom: 16,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  halfInputContainer: {
    flex: 0.48,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationAddressDetails;
