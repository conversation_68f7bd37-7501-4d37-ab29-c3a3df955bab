import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Colors, Sizes } from '../../utils/constants/Theme';

interface CustomTabProps {
  label: string;
  active: boolean;
  onPress: () => void;
}

const CustomTab: React.FC<CustomTabProps> = ({ label, active, onPress }) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.tab, active ? styles.activeTab : styles.inactiveTab]}>
      <Text style={[styles.tabText, active ? styles.activeText : styles.inactiveText]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    backgroundColor: '#FFFFFF', // Filled white background
  },
  inactiveTab: {
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR, // Same as tabsContainer bg
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  activeText: {
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    fontSize:Sizes.XLARGE,
  },
  inactiveText: {
    fontSize:Sizes.XLARGE,
    color: Colors.COMMON_GREY_SHADE_LIGHT, // Inactive text color
  },
});

export default CustomTab;
