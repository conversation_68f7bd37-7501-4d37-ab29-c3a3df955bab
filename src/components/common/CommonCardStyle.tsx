import React from 'react';
import { View, Text, StyleSheet, Image, ImageSourcePropType } from 'react-native';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';

interface CommonCardStyleProps {
    header: string;
    headerColor?: string;
    textColor?: string;
    children?: React.ReactNode;
    isCardContainerDecorated?: boolean;
    isTitleBordered?: boolean;
    cardBackgroundColor?: string;
    showException?: boolean;
    exceptionText?: string;
    exceptionIcon?: ImageSourcePropType;
    headerBottomPadding?: boolean;
    headerTextSize?: number;
    style?: object;
    marginVertical?: number;
}

const CommonCardStyle = ({
    header,
    headerColor,
    textColor,
    children,
    style,
    isCardContainerDecorated = false,
    isTitleBordered,
    cardBackgroundColor,
    showException = false,
    exceptionText = 'No Data Found',
    exceptionIcon,
    headerBottomPadding = false,
    headerTextSize = Sizes.MEDIUM,
    marginVertical = 4,
}: CommonCardStyleProps) => {

    const renderContent = () => {
        if (showException) {
            return (
                <View style={styles.exceptionContainer}>
                    {exceptionIcon && (
                        <Image
                            source={exceptionIcon}
                            style={styles.exceptionImage}
                            resizeMode="contain"
                        />
                    )}
                    <View style={styles.textContainer}>
                        <Text style={styles.sorryText}>SORRY</Text>
                        <Text style={styles.exceptionText}>{exceptionText}</Text>
                    </View>
                </View>
            );
        }
        return children;
    };

    return (
        <View style={[styles.card,{ marginVertical: marginVertical }]}>
            <View style={[
                styles.header,
                { backgroundColor: headerColor },
                isTitleBordered && styles.headerBordered,
                 headerBottomPadding && styles.headerWithBottomPadding,
            ]}>
                <Text style={[styles.headerText, { color: textColor,fontSize: headerTextSize }]}>{header}</Text>
            </View>
             <View
        style={[
          styles.body,
          isCardContainerDecorated && styles.bodyDecorated,
          cardBackgroundColor && { backgroundColor: cardBackgroundColor },
          style && style, // Check if style is not null or undefined
        ]}>
                {renderContent()}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        width: '95%',
        borderRadius: 2,
        overflow: 'hidden',
        elevation: 2,
        backgroundColor: Colors.SECONDARY,
        borderWidth: 1,
        borderColor: Colors.COMMON_GREY_SHADE_DARK,
    },
    header: {
        padding: 8,
    },
     headerWithBottomPadding: {
        paddingBottom: 12,
    },
    headerText: {
        color: '#fff',
        fontWeight: '700',
        fontSize: Sizes.LARGE,
        fontFamily: Fonts.ROBO_REGULAR,
    },
    body: {
        paddingBottom: 6,
        borderRadius: 2,
        backgroundColor: 'transparent',
    },
    bodyDecorated: {
        margin: 8,
    },
    headerBordered: {
        borderBottomWidth: 2,
        borderBottomColor: Colors.COMMON_GREY_SHADE_DARK,
    },
    // Exception View Styles
    exceptionContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row', // Keep as row for horizontal layout
        paddingVertical: 20,
        minHeight: 180,
    },
    exceptionImage: {
        width: 80,
        height: 80,
        marginRight: 16, // Space between icon and text
    },
    textContainer: {
        flexDirection: 'column', // Stack SORRY and message vertically
        alignItems: 'flex-start',
    },
    sorryText: {
        fontSize: 24,
        color: '#E53E3E',
        marginBottom: 4,
        fontFamily: Fonts.ROBO_REGULAR,
    },
    exceptionText: {
        fontSize: 16,
        fontWeight: '900',
        color: '#666',
        fontFamily: Fonts.ROBO_BOLD,
    },
});

export default CommonCardStyle;